pull_request_rules:
- name: label-documentation
  description: Automatically apply documentation label
  conditions:
    - or:
      - files~=^[^/]+\.md$
      - files~=^docs/
      - files~=^examples/
  actions:
    label:
      add:
        - documentation

- name: label-ci-build
  description: Automatically apply ci/build label
  conditions:
    - or:
      - files~=^\.github/
      - files~=\.buildkite/
      - files~=^cmake/
      - files=CMakeLists.txt
      - files~=^docker/Dockerfile
      - files~=^requirements.*\.txt
      - files=setup.py
  actions:
    label:
      add:
        - ci/build

- name: label-deepseek
  description: Automatically apply deepseek label
  conditions:
    - or:
      - files~=^examples/.*deepseek.*\.py
      - files~=^tests/.*deepseek.*\.py
      - files~=^vllm/entrypoints/openai/tool_parsers/.*deepseek.*\.py
      - files~=^vllm/model_executor/models/.*deepseek.*\.py
      - files~=^vllm/reasoning/.*deepseek.*\.py
      - files~=^vllm/transformers_utils/.*deepseek.*\.py
      - title~=(?i)DeepSeek
  actions:
    label:
      add:
        - deepseek

- name: label-frontend
  description: Automatically apply frontend label
  conditions:
    - files~=^vllm/entrypoints/
  actions:
    label:
      add:
        - frontend

- name: label-llama
  description: Automatically apply llama label
  conditions:
    - or:
      - files~=^examples/.*llama.*\.py
      - files~=^tests/.*llama.*\.py
      - files~=^vllm/entrypoints/openai/tool_parsers/llama.*\.py
      - files~=^vllm/model_executor/models/.*llama.*\.py
      - files~=^vllm/transformers_utils/configs/.*llama.*\.py
      - title~=(?i)llama
  actions:
    label:
      add:
        - llama

- name: label-multi-modality
  description: Automatically apply multi-modality label
  conditions:
    - or:
      - files~=^vllm/multimodal/
      - files~=^tests/multimodal/
      - files~=^tests/models/multimodal/
      - files=tests/models/test_vision.py
  actions:
    label:
      add:
        - multi-modality

- name: label-new-model
  description: Automatically apply new-model label
  conditions:
    - and:
      - files~=^vllm/model_executor/models/
      - files=vllm/model_executor/models/registry.py
  actions:
    label:
      add:
        - new-model

- name: label-performance
  description: Automatically apply performance label
  conditions:
    - or:
      - files~=^benchmarks/
      - files~=^vllm/benchmarks/
      - files~=^tests/benchmarks/
      - files~=^\.buildkite/nightly-benchmarks/
  actions:
    label:
      add:
        - performance

- name: label-qwen
  description: Automatically apply qwen label
  conditions:
    - or:
      - files~=^examples/.*qwen.*\.py
      - files~=^tests/.*qwen.*\.py
      - files~=^vllm/model_executor/models/.*qwen.*\.py
      - files~=^vllm/reasoning/.*qwen.*\.py
      - title~=(?i)Qwen
  actions:
    label:
      add:
        - qwen

- name: label-rocm
  description: Automatically apply rocm label
  conditions:
    - or:
      - files~=^csrc/rocm/
      - files~=^docker/Dockerfile.rocm
      - files~=^requirements/rocm.*\.txt
      - files~=^vllm/attention/backends/rocm.*\.py
      - files~=^vllm/attention/ops/rocm.*\.py
      - files~=^vllm/model_executor/layers/fused_moe/rocm.*\.py
      - files~=^vllm/v1/attention/backends/mla/rocm.*\.py
      - files~=^tests/kernels/.*_rocm.*\.py
      - files=vllm/platforms/rocm.py
      - title~=(?i)AMD
      - title~=(?i)ROCm
  actions:
    label:
      add:
        - rocm

- name: label-structured-output
  description: Automatically apply structured-output label
  conditions:
    - or:
      - files~=^benchmarks/structured_schemas/
      - files=benchmarks/benchmark_serving_structured_output.py
      - files=benchmarks/run_structured_output_benchmark.sh
      - files=docs/features/structured_outputs.md
      - files=examples/offline_inference/structured_outputs.py
      - files=examples/online_serving/openai_chat_completion_structured_outputs.py
      - files=examples/online_serving/openai_chat_completion_structured_outputs_with_reasoning.py
      - files~=^vllm/model_executor/guided_decoding/
      - files=tests/model_executor/test_guided_processors.py
      - files=tests/entrypoints/llm/test_guided_generate.py
      - files~=^tests/v1/structured_output/
      - files=tests/v1/entrypoints/llm/test_guided_generate.py
      - files~=^vllm/v1/structured_output/
  actions:
    label:
      add:
        - structured-output

- name: label-speculative-decoding
  description: Automatically apply speculative-decoding label
  conditions:
    - or:
      - files~=^vllm/spec_decode/
      - files~=^vllm/v1/spec_decode/
      - files=vllm/model_executor/layers/spec_decode_base_sampler.py
      - files~=^tests/spec_decode/
      - files~=^tests/v1/spec_decode/
      - files~=^examples/.*(spec_decode|mlpspeculator|eagle|speculation).*\.py
      - files~=^vllm/model_executor/models/.*eagle.*\.py
      - files=vllm/model_executor/models/mlp_speculator.py
      - files~=^vllm/transformers_utils/configs/(eagle|medusa|mlp_speculator)\.py
  actions:
    label:
      add:
        - speculative-decoding

- name: label-v1
  description: Automatically apply v1 label
  conditions:
    - or:
      - files~=^vllm/v1/
      - files~=^tests/v1/
  actions:
    label:
      add:
        - v1

- name: label-tpu
  description: Automatically apply tpu label
  # Keep this list in sync with `label-tpu-remove` conditions
  conditions:
    - or:
      - files~=tpu.py
      - files~=_tpu
      - files~=tpu_
      - files~=/tpu/
      - files~=pallas
  actions:
    label:
      add:
        - tpu

- name: label-tpu-remove
  description: Automatically remove tpu label
  # Keep this list in sync with `label-tpu` conditions
  conditions:
    - and:
      - -files~=tpu.py
      - -files~=_tpu
      - -files~=tpu_
      - -files~=/tpu/
      - -files~=pallas
  actions:
    label:
      remove:
        - tpu

- name: label-tool-calling
  description: Automatically add tool-calling label
  conditions:
    - or:
      - files~=^tests/tool_use/
      - files~=^tests/mistral_tool_use/
      - files~=^tests/entrypoints/openai/tool_parsers/
      - files=tests/entrypoints/openai/test_chat_with_tool_reasoning.py
      - files~=^vllm/entrypoints/openai/tool_parsers/
      - files=docs/features/tool_calling.md
      - files~=^examples/tool_chat_*
      - files=examples/offline_inference/chat_with_tools.py
      - files=examples/online_serving/openai_chat_completion_client_with_tools_required.py
      - files=examples/online_serving/openai_chat_completion_tool_calls_with_reasoning.py
      - files=examples/online_serving/openai_chat_completion_client_with_tools.py
  actions:
    label:
      add:
        - tool-calling

- name: ping author on conflicts and add 'needs-rebase' label
  conditions:
      - conflict
      - -closed
  actions:
    label:
      add:
        - needs-rebase
    comment:
      message: |
       This pull request has merge conflicts that must be resolved before it can be
       merged. Please rebase the PR, @{{author}}.

       https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/working-with-forks/syncing-a-fork

- name: assign reviewer for tensorizer changes
  conditions:
      - files~=^vllm/model_executor/model_loader/tensorizer.py
      - files~=^vllm/model_executor/model_loader/tensorizer_loader.py
      - files~=^tests/entrypoints/openai/test_tensorizer_entrypoint.py
      - files~=^tests/tensorizer_loader/
  actions:
    assign:
      users:
        - "sangstar"

- name: remove 'needs-rebase' label when conflict is resolved
  conditions:
      - -conflict
      - -closed
  actions:
    label:
      remove:
        - needs-rebase
